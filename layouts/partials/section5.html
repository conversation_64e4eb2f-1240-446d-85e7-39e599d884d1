{{- $section5 := .Site.Params.section5 }} {{- if eq $section5 true }} {{-
$section5 = dict "_" "_" }} {{- end }} {{- $title := index $section5 "title" |
default "Drop us a line or two "}} {{- $subtitle := index $section5 "subtitle" |
default "We'd love to hear from you" }} {{- $action := index $section5 "action"
}} {{- $method := index $section5 "method" }} {{- $buttonText := index $section5
"buttontext" | default "Send Message" }}

<section class="section section-light-grey is-medium" id="section5">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 is-spaced">{{ $title }}</h2>
      <h3 class="subtitle is-5 is-muted">{{ $subtitle }}</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-6 is-offset-3">
          <!-- Success Message (initially hidden) -->
          <div
            id="form-success"
            class="notification is-success"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-success').style.display='none'"
            ></button>
            <strong>Thank you!</strong> Your message has been sent successfully.
            We'll get back to you soon.
          </div>

          <!-- Error Message (initially hidden) -->
          <div
            id="form-error"
            class="notification is-danger"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-error').style.display='none'"
            ></button>
            There was a problem submitting your form. Please try again.
          </div>

          <form
            id="contact-form"
            {{
            with
            $action
            }}
            action="{{ . }}"
            {{end}}{{
            with
            $method
            }}
            method="{{ . }}"
            {{end}}
          >
            <div class="columns is-multiline">
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="name"
                  type="text"
                  placeholder="Enter your name"
                  required
                />
              </div>
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  required
                />
              </div>
              <div class="column is-12">
                <input
                  class="input is-medium"
                  name="subject"
                  type="text"
                  placeholder="Subject"
                />
              </div>
              <div class="column is-12">
                <textarea
                  class="textarea"
                  name="message"
                  rows="10"
                  placeholder="Write something..."
                  required
                ></textarea>
              </div>
              <!--honeypot field to prevent spam -->
              <input type="text" name="_gotcha" style="display: none" />
              <!-- bot detection data for server-side validation -->
              <input type="hidden" name="_bot_detection_data" id="section5-bot-detection-data" />
              <div class="form-footer has-text-centered mt-10">
                <button
                  id="submit-button"
                  class="button cta is-large primary-btn raised is-clear"
                  type="submit"
                >
                  {{ $buttonText }}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Bot Detection Library -->
<script src="/js/bot-detection.js"></script>

<script>
  var form, submitButton, successMessage, errorMessage, botDetection;

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("contact-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");

    // Initialize bot detection
    botDetection = new BotDetection("contact-form", "section5-bot-detection-data");
    botDetection.init();

    // Add form submit event listener
    form.addEventListener("submit", function(e) {
      e.preventDefault();
      submitForm();
    });
  });



  async function submitForm() {
    // Use the shared bot detection form submission handler
    await submitFormWithBotDetection(
      botDetection,
      submitButton,
      successMessage,
      errorMessage
    );
  }
</script>
