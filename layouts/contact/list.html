{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium contact-page">
  <div class="container">
    <div class="columns">
      <div class="column is-8  is-offset-2  is-centered-tablet has-text-centered">
        <!-- <h1 class="title section-title">{{ .Title }}</h1> -->
        <!-- <h5 class="subtitle is-5 is-muted">{{ .Params.Description }}</h5> -->
        <h1 class="title section-title">Talk to Us — We Don't Byte!</h1>
        <h5 class="subtitle is-5 is-muted">Whether you're lost in the cloud or just want to say hi, we're all ears (and fiber optics). Our team at <strong>whitesky.cloud</strong> is here to answer questions, untangle tech troubles, and occasionally explain what a hypervisor is.</h5>
        <div class="divider is-centered"></div>
      </div>
    </div>

    <div class="content">
      <div class="columns">
        <div class="column is-8 is-offset-2">
          <!-- Contact Introduction -->
          <!-- <div class="content contact-intro">
            <h2>Talk to Us — We Don't Byte!</h2>
            <p>Whether you're lost in the cloud or just want to say hi, we're all ears (and fiber optics). Our team at <strong>whitesky.cloud</strong> is here to answer questions, untangle tech troubles, and occasionally explain what a hypervisor is.</p>
          </div> -->

          <!-- Contact Form -->
          <div class="contact-form">
            <!-- Success Message (initially hidden) -->
            <div
              id="form-success"
              class="form-message success"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-success').style.display='none'"
              >&times;</button>
              <strong>Thank you!</strong> Your message has been sent successfully.
              We'll get back to you soon.
            </div>

            <!-- Error Message (initially hidden) -->
            <div
              id="form-error"
              class="form-message error"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-error').style.display='none'"
              >&times;</button>
              There was a problem submitting your message. Please try again.
            </div>

            <h3 class="form-title">General Inquiries</h3>
            <p class="form-subtitle">Got a question? Want to explore our services? Or just wondering if cloud platforms dream of electric sheep? Fill in the form below and our team will get back to you faster than a ping on a local network.</p>

            <form
              id="contact-form"
              action="{{ .Site.Params.section5.action }}"
              method="POST"
            >
              <div class="columns is-multiline">
                <!-- Name and Email Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Your Name</label>
                    <input
                      class="input"
                      name="name"
                      type="text"
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Email Address</label>
                    <input
                      class="input"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                </div>
                <!-- Phone and Subject Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Phone Number</label>
                    <input
                      class="input"
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Subject</label>
                    <input
                      class="input"
                      name="subject"
                      type="text"
                      placeholder="Subject"
                    />
                  </div>
                </div>
                <!-- Message -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Your Message</label>
                    <textarea
                      class="textarea"
                      name="message"
                      rows="6"
                      placeholder="Write something..."
                      required
                    ></textarea>
                  </div>
                </div>

                <!-- Hidden fields for backend -->
                <!--honeypot field to prevent spam -->
                <input type="text" name="_gotcha" style="display: none" />
                <!-- bot detection data for server-side validation -->
                <input type="hidden" name="_bot_detection_data" id="contact-bot-detection-data" />

                <div class="column is-12">
                  <button
                    id="submit-button"
                    class="submit-button button cta is-large primary-btn raised is-clear"
                    type="submit"
                  >
                    Send Message
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- Contact Information Sections -->
          <div class="contact-info-sections">
            <!-- Support Section -->
            <div class="info-section">
              <h2>Support</h2>
              <p>If you need technical support, want more information about our services, or are considering a collaboration — we're here to help.</p>
              <ul class="contact-list">
                <li>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>📞 24/7 Phone (for critical incidents): <a href="tel:+3256740695">+32 56 740 695</a></li>
              </ul>
            </div>

            <!-- Data Privacy & Security Section -->
            <div class="info-section">
              <h2>Data Privacy & Security</h2>
              <p>🔐 <strong>Got a privacy concern or a question about information security?</strong></p>
              <p>Please contact us via:</p>
              <ul class="contact-list">
                <li><strong>Email</strong>: <a href="mailto:<EMAIL>"><EMAIL></a></li>
              </ul>
              <p><strong>Or contact our COO directly:</strong></p>
              <ul class="contact-list">
                <li><strong>Xavier Keters</strong></li>
                <li>📞 +32 477 68 51 95</li>
                <li>📧 <a href="mailto:<EMAIL>"><EMAIL></a></li>
              </ul>
              <p><em>We take your data seriously — even if you joke about it in the subject line.</em></p>
            </div>

            <!-- Headquarters Section -->
            <div class="info-section">
              <h2>Our HQ</h2>
              <div class="hq-info">
                <p>🏢 <strong>whitesky headquarters</strong><br>
                Antwerpsesteenweg 19<br>
                9080 Lochristi, Belgium</p>
                <p>📞 <strong>Phone</strong>: +32 (0)9 324 20 90<br>
                📧 <strong>Email</strong>: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><em>We're headquartered in Belgium, but our cloud floats globally 🌍</em></p>
              </div>
            </div>

            <!-- Management Contacts Section -->
            <div class="info-section">
              <h2>Management Contacts</h2>
              <ul class="contact-list">
                <li>Hans van Linschoten, CEO - <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>Geert Audenaert, CTO - <a href="mailto:<EMAIL>"><EMAIL></a></li>
              </ul>
            </div>

            <!-- Commercial Contacts Section -->
            <div class="info-section">
              <h2>Commercial Contacts</h2>
              <ul class="contact-list">
                <li>Europe: Xavier Keters - <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>Africa: Hans van Linschoten - <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>The Americas: Javier Vargas - <a href="mailto:<EMAIL>"><EMAIL></a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Bot Detection Library -->
<script src="/js/bot-detection.js"></script>

<script>
  var form, submitButton, successMessage, errorMessage, botDetection;

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("contact-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");

    // Initialize bot detection
    botDetection = new BotDetection("contact-form", "contact-bot-detection-data");
    botDetection.init();

    // Add form submit event listener
    form.addEventListener("submit", function(e) {
      e.preventDefault();
      submitForm();
    });
  });

  async function submitForm() {
    // Use the shared bot detection form submission handler
    await submitFormWithBotDetection(
      botDetection,
      submitButton,
      successMessage,
      errorMessage
    );
  }
</script>

{{ partial "footer.html" . }}

{{ end }}
