{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium pricing-page">
  <div class="container">
    <div class="columns">
        <div class="column  is-8  is-offset-2 is-centered-tablet">
          <h1 class="title section-title">{{ .Title }}</h1>
          <h5 class="subtitle is-5 is-muted">{{ .Params.Subtitle }}</h5>
          <div class="divider"></div>
        </div>
      </div>

      <div class="content">
        <div class="columns">
        <div class="column is-8 is-offset-2">
          <div class="content">
            <p>We believe cloud pricing should be as flexible as the infrastructure itself.</p>
            <p>Rather than fixed plans, we offer custom quotes based on your specific needs — whether you're running a few virtual machines or building a sovereign, multi-tenant cloud.</p>
            <p>Tell us how much capacity you need below, and we'll provide a tailored proposal.</p>
          </div>

          <div class="quote-form">
            <!-- Success Message (initially hidden) -->
            <div
              id="form-success"
              class="form-message success"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-success').style.display='none'"
              >&times;</button>
              <strong>Thank you!</strong> Your quote request has been submitted successfully.
              We'll get back to you within 1 business day.
            </div>

            <!-- Error Message (initially hidden) -->
            <div
              id="form-error"
              class="form-message error"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-error').style.display='none'"
              >&times;</button>
              There was a problem submitting your quote request. Please try again.
            </div>

            <h3 class="form-title">Quote Request Form</h3>

            <form
              id="pricing-form"
              action="{{ .Site.Params.section5.action }}"
              method="POST"
            >
              <div class="columns is-multiline">
                <!-- Name and Email Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Your Name</label>
                    <input
                      class="input"
                      name="name"
                      type="text"
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Email Address</label>
                    <input
                      class="input"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                </div>
                <!-- Phone and Company Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Phone Number</label>
                    <input
                      class="input"
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Company Name</label>
                    <input
                      class="input"
                      id="company"
                      name="company"
                      type="text"
                      placeholder="Enter your company name"
                    />
                  </div>
                </div>
                <!-- Technical Requirements -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Number of vCPUs</label>
                    <input
                      class="input"
                      id="vcpus"
                      name="vcpus"
                      type="number"
                      min="1"
                      placeholder="e.g., 16"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Memory (in GB)</label>
                    <input
                      class="input"
                      id="memory"
                      name="memory"
                      type="number"
                      min="1"
                      placeholder="e.g., 64"
                      required
                    />
                  </div>
                </div>

                <div class="column is-6">
                  <div class="field">
                    <label class="label">Block Storage (in TB)</label>
                    <input
                      class="input"
                      id="blockStorage"
                      name="blockStorage"
                      type="number"
                      min="0"
                      placeholder="e.g., 2"
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Object Storage (in TB)</label>
                    <input
                      class="input"
                      id="objectStorage"
                      name="objectStorage"
                      type="number"
                      min="0"
                      placeholder="e.g., 10"
                    />
                  </div>
                </div>

                <!-- Service Model -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Service Model</label>
                    <div class="radio-group" style="display: flex;">
                      <div class="radio-option">
                        <input type="radio" id="self-managed" name="serviceModel" value="self-managed" required>
                        <label for="self-managed">Self Managed Cloud</label>
                      </div>
                      <div class="radio-option" style="margin-left: 15px;">
                        <input type="radio" id="caas" name="serviceModel" value="caas" required>
                        <label for="caas">Cloud as a Service</label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Hardware Quote Checkbox -->
                <div class="column is-12">
                  <div class="checkbox-field">
                    <input type="checkbox" id="hardwareQuote" name="hardwareQuote">
                    <label for="hardwareQuote">I would like to receive a hardware quote as well</label>
                  </div>
                  <p class="form-note">* Hardware quotes are delivered by a trusted partner of whitesky.cloud, not by whitesky.cloud itself.</p>
                </div>

                <!-- Additional Information -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Additional Requests or Information</label>
                    <textarea
                      class="textarea"
                      id="additionalInfo"
                      name="additionalInfo"
                      rows="4"
                      placeholder="e.g., Need GPU support (NVIDIA A100), or Database as a Service..."
                    ></textarea>
                  </div>
                </div>

                <!-- Hidden fields for backend -->
                <input type="hidden" name="subject" value="Quote Request" />
                <!-- honeypot field to prevent spam -->
                <input type="text" name="_gotcha" style="display: none" />
                <!-- bot detection data for server-side validation -->
                <input type="hidden" name="_bot_detection_data" id="bot-detection-data" />

                <div class="column is-12">
                  <button
                    id="submit-button"
                    class="submit-button button cta is-large primary-btn raised is-clear"
                    type="submit"
                  >
                    Submit Quote Request
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- How It Works Section -->
          <div class="info-section">
            <h2>How It Works</h2>
            <ul>
              <li>You fill in your capacity needs</li>
              <li>We calculate a tailored, no-obligation quote</li>
              <li>We'll reach out personally within 1 business day</li>
            </ul>

            <h3>Why We Do It This Way</h3>
            <p>Our platform supports private, public, and hybrid cloud models — and we want to make sure you get the best setup at the right price. By understanding your requirements, we can recommend the ideal combination of performance, resilience, and cost.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Bot Detection Library -->
<script src="/js/bot-detection.js"></script>

<script>
  var form, submitButton, successMessage, errorMessage, botDetection;

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("pricing-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");

    // Initialize bot detection
    botDetection = new BotDetection("pricing-form", "bot-detection-data");
    botDetection.init();

    // Add form submit event listener
    form.addEventListener("submit", function(e) {
      e.preventDefault();
      submitForm();
    });
  });

  async function submitForm() {
    // Use the shared bot detection form submission handler
    await submitFormWithBotDetection(
      botDetection,
      submitButton,
      successMessage,
      errorMessage
    );
  }

</script>

{{ partial "footer.html" . }}

{{ end }}
