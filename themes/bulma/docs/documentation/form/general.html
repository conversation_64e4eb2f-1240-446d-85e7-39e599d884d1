---
title: Form controls
layout: documentation
doc-tab: form
doc-subtab: general
breadcrumb:
- home
- documentation
- form
- form-general
variables_control_keys:
- $control-radius
- $control-radius-small
- $control-padding-vertical
- $control-padding-horizontal
variables_keys:
- $label-color
- $label-weight
- $help-size
---

{% capture example %}
<div class="field">
  <label class="label">Name</label>
  <div class="control">
    <input class="input" type="text" placeholder="Text input">
  </div>
</div>

<div class="field">
  <label class="label">Username</label>
  <div class="control has-icons-left has-icons-right">
    <input class="input is-success" type="text" placeholder="Text input" value="bulma">
    <span class="icon is-small is-left">
      <i class="fas fa-user"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-check"></i>
    </span>
  </div>
  <p class="help is-success">This username is available</p>
</div>

<div class="field">
  <label class="label">Email</label>
  <div class="control has-icons-left has-icons-right">
    <input class="input is-danger" type="email" placeholder="Email input" value="hello@">
    <span class="icon is-small is-left">
      <i class="fas fa-envelope"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-exclamation-triangle"></i>
    </span>
  </div>
  <p class="help is-danger">This email is invalid</p>
</div>

<div class="field">
  <label class="label">Subject</label>
  <div class="control">
    <div class="select">
      <select>
        <option>Select dropdown</option>
        <option>With options</option>
      </select>
    </div>
  </div>
</div>

<div class="field">
  <label class="label">Message</label>
  <div class="control">
    <textarea class="textarea" placeholder="Textarea"></textarea>
  </div>
</div>

<div class="field">
  <div class="control">
    <label class="checkbox">
      <input type="checkbox">
      I agree to the <a href="#">terms and conditions</a>
    </label>
  </div>
</div>

<div class="field">
  <div class="control">
    <label class="radio">
      <input type="radio" name="question">
      Yes
    </label>
    <label class="radio">
      <input type="radio" name="question">
      No
    </label>
  </div>
</div>

<div class="field is-grouped">
  <div class="control">
    <button class="button is-link">Submit</button>
  </div>
  <div class="control">
    <button class="button is-link is-light">Cancel</button>
  </div>
</div>
{% endcapture %}

{% capture control_example %}
<div class="control">
  <input class="input" type="text" placeholder="Text input">
</div>
{% endcapture %}

{% capture field_example %}
<div class="field">
  <label class="label">Label</label>
  <div class="control">
    <input class="input" type="text" placeholder="Text input">
  </div>
  <p class="help">This is a help text</p>
</div>
{% endcapture %}

{% capture fields_example %}
<div class="field">
  <label class="label">Name</label>
  <div class="control">
    <input class="input" type="text" placeholder="e.g Alex Smith">
  </div>
</div>

<div class="field">
  <label class="label">Email</label>
  <div class="control">
    <input class="input" type="email" placeholder="e.g. <EMAIL>">
  </div>
</div>
{% endcapture %}

{% capture control_input_example %}
<div class="control">
  <input class="input" type="text" placeholder="Text input">
</div>
{% endcapture %}

{% capture control_select_example %}
<div class="control">
  <div class="select">
    <select>
      <option>Select dropdown</option>
      <option>With options</option>
    </select>
  </div>
</div>
{% endcapture %}

{% capture control_button_example %}
<div class="control">
  <button class="button is-primary">Submit</button>
</div>
{% endcapture %}

{% capture icons_example %}
<div class="field">
  <p class="control has-icons-left has-icons-right">
    <input class="input" type="email" placeholder="Email">
    <span class="icon is-small is-left">
      <i class="fas fa-envelope"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-check"></i>
    </span>
  </p>
</div>
<div class="field">
  <p class="control has-icons-left">
    <input class="input" type="password" placeholder="Password">
    <span class="icon is-small is-left">
      <i class="fas fa-lock"></i>
    </span>
  </p>
</div>
<div class="field">
  <p class="control">
    <button class="button is-success">
      Login
    </button>
  </p>
</div>
{% endcapture %}

{% capture select_icons_example %}
<div class="field">
  <p class="control has-icons-left">
    <span class="select">
      <select>
        <option selected>Country</option>
        <option>Select dropdown</option>
        <option>With options</option>
      </select>
    </span>
    <span class="icon is-small is-left">
      <i class="fas fa-globe"></i>
    </span>
  </p>
</div>
{% endcapture %}

{% capture has_icons_small_example %}
<div class="field">
  <label class="label is-small">Small input</label>
  <div class="control has-icons-left has-icons-right">
    <input class="input is-small" type="email" placeholder="Normal">
    <span class="icon is-small is-left">
      <i class="fas fa-envelope"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-check"></i>
    </span>
  </div>
</div>
{% endcapture %}

{% capture has_icons_normal_example %}
<div class="field">
  <label class="label">Normal input</label>
  <div class="control has-icons-left has-icons-right">
    <input class="input" type="email" placeholder="Extra small">
    <span class="icon is-small is-left">
      <i class="fas fa-envelope fa-xs"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-check fa-xs"></i>
    </span>
  </div>
</div>

<div class="field">
  <div class="control has-icons-left has-icons-right">
    <input class="input" type="email" placeholder="Normal">
    <span class="icon is-left">
      <i class="fas fa-envelope"></i>
    </span>
    <span class="icon is-right">
      <i class="fas fa-check"></i>
    </span>
  </div>
</div>
{% endcapture %}

{% capture has_icons_medium_example %}
<div class="field">
  <label class="label is-medium">Medium input</label>
  <div class="control has-icons-left has-icons-right">
    <input class="input is-medium" type="email" placeholder="Extra small">
    <span class="icon is-small is-left">
      <i class="fas fa-envelope fa-xs"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-check fa-xs"></i>
    </span>
  </div>
</div>

<div class="field">
  <div class="control has-icons-left has-icons-right">
    <input class="input is-medium" type="email" placeholder="Small">
    <span class="icon is-left">
      <i class="fas fa-envelope fa-sm"></i>
    </span>
    <span class="icon is-right">
      <i class="fas fa-check fa-sm"></i>
    </span>
  </div>
</div>

<div class="field">
  <div class="control has-icons-left has-icons-right">
    <input class="input is-medium" type="email" placeholder="Normal">
    <span class="icon is-medium is-left">
      <i class="fas fa-envelope"></i>
    </span>
    <span class="icon is-medium is-right">
      <i class="fas fa-check"></i>
    </span>
  </div>
</div>
{% endcapture %}

{% capture has_icons_large_example %}
<div class="field">
  <label class="label is-large">Large input</label>
  <div class="control has-icons-left has-icons-right">
    <input class="input is-large" type="email" placeholder="Extra small">
    <span class="icon is-small is-left">
      <i class="fas fa-envelope fa-xs"></i>
    </span>
    <span class="icon is-small is-right">
      <i class="fas fa-check fa-xs"></i>
    </span>
  </div>
</div>

<div class="field">
  <div class="control has-icons-left has-icons-right">
    <input class="input is-large" type="email" placeholder="Small">
    <span class="icon is-left">
      <i class="fas fa-envelope fa-sm"></i>
    </span>
    <span class="icon is-right">
      <i class="fas fa-check fa-sm"></i>
    </span>
  </div>
</div>

<div class="field">
  <div class="control has-icons-left has-icons-right">
    <input class="input is-large" type="email" placeholder="Normal">
    <span class="icon is-large is-left">
      <i class="fas fa-envelope"></i>
    </span>
    <span class="icon is-large is-right">
      <i class="fas fa-check"></i>
    </span>
  </div>
</div>

<div class="field">
  <div class="control has-icons-left has-icons-right">
    <input class="input is-large" type="email" placeholder="Large">
    <span class="icon is-medium is-left">
      <i class="fas fa-envelope fa-lg"></i>
    </span>
    <span class="icon is-medium is-right">
      <i class="fas fa-check fa-lg"></i>
    </span>
  </div>
</div>
{% endcapture %}

{% capture addons_example %}
<div class="field has-addons">
  <div class="control">
    <input class="input" type="text" placeholder="Find a repository">
  </div>
  <div class="control">
    <a class="button is-info">
      Search
    </a>
  </div>
</div>
{% endcapture %}

{% capture addons_static_example %}
<div class="field has-addons">
  <p class="control">
    <input class="input" type="text" placeholder="Your email">
  </p>
  <p class="control">
    <a class="button is-static">
      @gmail.com
    </a>
  </p>
</div>
{% endcapture %}

{% capture addons_expanded_example %}
<div class="field has-addons">
  <p class="control">
    <span class="select">
      <select>
        <option>$</option>
        <option>£</option>
        <option>€</option>
      </select>
    </span>
  </p>
  <p class="control">
    <input class="input" type="text" placeholder="Amount of money">
  </p>
  <p class="control">
    <a class="button">
      Transfer
    </a>
  </p>
</div>

<div class="field has-addons">
  <p class="control">
    <span class="select">
      <select>
        <option>$</option>
        <option>£</option>
        <option>€</option>
      </select>
    </span>
  </p>
  <p class="control is-expanded">
    <input class="input" type="text" placeholder="Amount of money">
  </p>
  <p class="control">
    <a class="button">
      Transfer
    </a>
  </p>
</div>
{% endcapture %}

{% capture addons_expanded_fullwidth_example %}
<div class="field has-addons">
  <div class="control is-expanded">
    <div class="select is-fullwidth">
      <select name="country">
        <option value="Argentina">Argentina</option>
        <option value="Bolivia">Bolivia</option>
        <option value="Brazil">Brazil</option>
        <option value="Chile">Chile</option>
        <option value="Colombia">Colombia</option>
        <option value="Ecuador">Ecuador</option>
        <option value="Guyana">Guyana</option>
        <option value="Paraguay">Paraguay</option>
        <option value="Peru">Peru</option>
        <option value="Suriname">Suriname</option>
        <option value="Uruguay">Uruguay</option>
        <option value="Venezuela">Venezuela</option>
      </select>
    </div>
  </div>
  <div class="control">
    <button type="submit" class="button is-primary">Choose</button>
  </div>
</div>
{% endcapture %}

{% capture addons_center_example %}
<div class="field has-addons has-addons-centered">
  <p class="control">
    <span class="select">
      <select>
        <option>$</option>
        <option>£</option>
        <option>€</option>
      </select>
    </span>
  </p>
  <p class="control">
    <input class="input" type="text" placeholder="Amount of money">
  </p>
  <p class="control">
    <a class="button is-primary">
      Transfer
    </a>
  </p>
</div>
{% endcapture %}

{% capture addons_right_example %}
<div class="field has-addons has-addons-right">
  <p class="control">
    <span class="select">
      <select>
        <option>$</option>
        <option>£</option>
        <option>€</option>
      </select>
    </span>
  </p>
  <p class="control">
    <input class="input" type="text" placeholder="Amount of money">
  </p>
  <p class="control">
    <a class="button is-primary">
      Transfer
    </a>
  </p>
</div>
{% endcapture %}

{% capture group_example %}
<div class="field is-grouped">
  <p class="control">
    <a class="button is-primary">
      Submit
    </a>
  </p>
  <p class="control">
    <a class="button is-light">
      Cancel
    </a>
  </p>
</div>
{% endcapture %}

{% capture group_centered_example %}
<div class="field is-grouped is-grouped-centered">
  <p class="control">
    <a class="button is-primary">
      Submit
    </a>
  </p>
  <p class="control">
    <a class="button is-light">
      Cancel
    </a>
  </p>
</div>
{% endcapture %}

{% capture group_right_example %}
<div class="field is-grouped is-grouped-right">
  <p class="control">
    <a class="button is-primary">
      Submit
    </a>
  </p>
  <p class="control">
    <a class="button is-light">
      Cancel
    </a>
  </p>
</div>
{% endcapture %}

{% capture group_expanded_example %}
<div class="field is-grouped">
  <p class="control is-expanded">
    <input class="input" type="text" placeholder="Find a repository">
  </p>
  <p class="control">
    <a class="button is-info">
      Search
    </a>
  </p>
</div>
{% endcapture %}

{% capture group_multiline_example %}
<div class="field is-grouped is-grouped-multiline">
  <p class="control">
    <a class="button">
      One
    </a>
  </p>
  <p class="control">
    <a class="button">
      Two
    </a>
  </p>
  <p class="control">
    <a class="button">
      Three
    </a>
  </p>
  <p class="control">
    <a class="button">
      Four
    </a>
  </p>
  <p class="control">
    <a class="button">
      Five
    </a>
  </p>
  <p class="control">
    <a class="button">
      Six
    </a>
  </p>
  <p class="control">
    <a class="button">
      Seven
    </a>
  </p>
  <p class="control">
    <a class="button">
      Eight
    </a>
  </p>
  <p class="control">
    <a class="button">
      Nine
    </a>
  </p>
  <p class="control">
    <a class="button">
      Ten
    </a>
  </p>
  <p class="control">
    <a class="button">
      Eleven
    </a>
  </p>
  <p class="control">
    <a class="button">
      Twelve
    </a>
  </p>
  <p class="control">
    <a class="button">
      Thirteen
    </a>
  </p>
</div>
{% endcapture %}

{% capture horizontal_form_example %}
<div class="field is-horizontal">
  <div class="field-label is-normal">
    <label class="label">From</label>
  </div>
  <div class="field-body">
    <div class="field">
      <p class="control is-expanded has-icons-left">
        <input class="input" type="text" placeholder="Name">
        <span class="icon is-small is-left">
          <i class="fas fa-user"></i>
        </span>
      </p>
    </div>
    <div class="field">
      <p class="control is-expanded has-icons-left has-icons-right">
        <input class="input is-success" type="email" placeholder="Email" value="<EMAIL>">
        <span class="icon is-small is-left">
          <i class="fas fa-envelope"></i>
        </span>
        <span class="icon is-small is-right">
          <i class="fas fa-check"></i>
        </span>
      </p>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label"></div>
  <div class="field-body">
    <div class="field is-expanded">
      <div class="field has-addons">
        <p class="control">
          <a class="button is-static">
            +44
          </a>
        </p>
        <p class="control is-expanded">
          <input class="input" type="tel" placeholder="Your phone number">
        </p>
      </div>
      <p class="help">Do not enter the first zero</p>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-normal">
    <label class="label">Department</label>
  </div>
  <div class="field-body">
    <div class="field is-narrow">
      <div class="control">
        <div class="select is-fullwidth">
          <select>
            <option>Business development</option>
            <option>Marketing</option>
            <option>Sales</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label">
    <label class="label">Already a member?</label>
  </div>
  <div class="field-body">
    <div class="field is-narrow">
      <div class="control">
        <label class="radio">
          <input type="radio" name="member">
          Yes
        </label>
        <label class="radio">
          <input type="radio" name="member">
          No
        </label>
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-normal">
    <label class="label">Subject</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <input class="input is-danger" type="text" placeholder="e.g. Partnership opportunity">
      </div>
      <p class="help is-danger">
        This field is required
      </p>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-normal">
    <label class="label">Question</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <textarea class="textarea" placeholder="Explain how we can help you"></textarea>
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label">
    <!-- Left empty for spacing -->
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <button class="button is-primary">
          Send message
        </button>
      </div>
    </div>
  </div>
</div>
{% endcapture %}

{% capture field_label_example %}
<div class="field is-horizontal">
  <div class="field-label">
    <label class="label">No padding</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <label class="checkbox">
          <input type="checkbox">
          Checkbox
        </label>
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-small">
    <label class="label">Small padding</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <input class="input is-small" type="text" placeholder="Small sized input">
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-normal">
    <label class="label">Normal label</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <input class="input" type="text" placeholder="Normal sized input">
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-medium">
    <label class="label">Medium label</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <input class="input is-medium" type="text" placeholder="Medium sized input">
      </div>
    </div>
  </div>
</div>

<div class="field is-horizontal">
  <div class="field-label is-large">
    <label class="label">Large label</label>
  </div>
  <div class="field-body">
    <div class="field">
      <div class="control">
        <input class="input is-large" type="text" placeholder="Large sized input">
      </div>
    </div>
  </div>
</div>
{% endcapture %}

{% capture disabled_fields_example %}
<fieldset disabled>
  {{ fields_example }}
</fieldset>
{% endcapture %}

<div class="content">
  <p>
    Bulma supports the following native <strong>HTML form elements</strong>: <code>&lt;form&gt;</code> <code>&lt;button&gt;</code> <code>&lt;input&gt;</code> <code>&lt;textarea&gt;</code> and <code>&lt;label&gt;</code>.
  </p>
  <p>
    The following CSS <strong>classes</strong> are supported:
  </p>
  <ul>
    <li><code>label</code></li>
    <li><code>input</code></li>
    <li><code>textarea</code></li>
    <li><code>select</code></li>
    <li><code>checkbox</code></li>
    <li><code>radio</code></li>
    <li><code>button</code></li>
    <li><code>help</code></li>
  </ul>
</div>

<div class="content">
  <p>
    To maintain an evenly balanced design, Bulma provides a very useful <code>control</code> container with which you can <strong>wrap</strong> the form controls.
    <br>
    When combining several controls in a <strong>form</strong>, use the <code>field</code> class as a <strong>container</strong>, to keep the spacing consistent.
  </p>
</div>

{% include elements/anchor.html name="Complete form example" %}

{% include elements/snippet.html content=example more=true %}

<div class="content">
  <p>
    For the best results using Bulma, it's recommended to use the <code>control</code> element as often as possible.
  </p>
</div>

{% include elements/anchor.html name="Form field" %}

<div class="content">
  <p>The <code>field</code> container is a simple container for:</p>
  <ul>
    <li>a text <code>label</code></li>
    <li>a form <code>control</code></li>
    <li>an optional <code>help</code>text</li>
  </ul>
</div>

{% include elements/snippet.html content=field_example %}

<div class="content">
  <p>This container allows form fields to be <strong>spaced consistently</strong>.</p>
</div>

{% include elements/snippet.html content=fields_example %}

{% include elements/anchor.html name="Form control" %}

<div class="content">
  <p>
    The Bulma <code>control</code> is a versatile <strong>block</strong> container meant to <strong>enhance single form controls</strong>. Because it has the same height as the element that it wraps, it can <strong class="has-text-danger">only contain</strong> the following Bulma elements:
  </p>

  <ul>
    <li><code>input</code></li>
    <li><code>select</code></li>
    <li><code>button</code></li>
    <li><code>icon</code></li>
  </ul>
  <p>
    This container gives the ability to:
  </p>
  <ul>
    <li>
      keep the <strong>spacing</strong> consistent
    </li>
    <li>
      combine form controls into a <strong>group</strong>
    </li>
    <li>
      combine form controls into a <strong>list</strong>
    </li>
    <li>
      append and prepend <strong>icons</strong> to a form control
    </li>
  </ul>
</div>

{% include elements/snippet.html content=control_input_example %}

{% include elements/snippet.html content=control_select_example %}

{% include elements/snippet.html content=control_button_example %}

<!-- Font Awesome Icons -->
{% include elements/anchor.html name="With icons" %}

<div class="content">
  <p>You can append one of 2 <strong>modifiers</strong> on a control:</p>
  <ul>
    <li>
      <code>has-icons-left</code>
    </li>
    <li>
      and/or <code>has-icons-right</code>
    </li>
  </ul>
  <p>You also need to add a modifier on the <strong>icon</strong>:</p>
  <ul>
    <li>
      <code>icon is-left</code> if <code>has-icons-left</code> is used
    </li>
    <li>
      <code>icon is-right</code> if <code>has-icons-right</code> is used
    </li>
  </ul>
  <p>The size of the <strong>input</strong> will define the size of the icon container.</p>
</div>

{% include elements/snippet.html content=icons_example clipped=true %}

<div class="content">
  <p>
    You can append icons to <strong>select dropdowns</strong> as well.
  </p>
</div>

{% include elements/snippet.html content=select_icons_example %}

<div class="content">
  <p>
    If the control contains an icon, Bulma will make sure the icon remains <strong>centered</strong>, no matter the size of the input <em>or</em> of the icon.
  </p>
</div>

{% include elements/snippet.html content=has_icons_small_example %}

{% include elements/snippet.html content=has_icons_normal_example clipped=true %}

{% include elements/snippet.html content=has_icons_medium_example clipped=true %}

{% include elements/snippet.html content=has_icons_large_example clipped=true %}

{% include elements/anchor.html name="Form addons" %}

<div class="content">
  <p>If you want to <strong>attach controls</strong> together, use the <code>has-addons</code> modifier on the <code>field</code> container:</p>
</div>

{% include elements/snippet.html content=addons_example %}

<div class="content">
  <p>You can attach inputs, buttons, and dropdowns <strong>only</strong>.</p>
</div>

<div class="content">
  <p>It can be useful to append a <a href="{{site.url}}/documentation/elements/button#static-button">static button</a>.</p>
</div>

{% include elements/snippet.html content=addons_static_example %}

<div class="content">
  <p>Use the <code>is-expanded</code> modifier on the element you want to fill up the remaining space (in this case, the input):</p>
</div>

{% include elements/snippet.html content=addons_expanded_example horizontal=true more=true %}

<div class="content">
  <p>If you want a full width select dropdown, pair <code>control is-expanded</code> with <code>select is-fullwidth</code>.</p>
</div>

{% include elements/snippet.html content=addons_expanded_fullwidth_example %}

<div class="content">
  <p>Use the <code>has-addons-centered</code> or the <code>has-addons-right</code> modifiers to alter the <strong>alignment</strong>.</p>
</div>

{% include elements/snippet.html content=addons_center_example %}

{% include elements/snippet.html content=addons_right_example %}

{% include elements/anchor.html name="Form group" %}

<div class="content">
  <p>
    If you want to <strong>group</strong> controls together, use the <code>is-grouped</code> modifier on the <code>field</code> container.
  </p>
</div>

{% include elements/snippet.html content=group_example %}

<div class="content">
    Use the <code>is-grouped-centered</code> or the <code>is-grouped-right</code> modifiers to alter the <strong>alignment</strong>.
  </p>
</div>

{% include elements/snippet.html content=group_centered_example %}

{% include elements/snippet.html content=group_right_example %}

<div class="content">
  <p>
    Add the <code>is-expanded</code> modifier on the control element you want to <strong>fill up the remaining space</strong> with.
  </p>
</div>

{% include elements/snippet.html content=group_expanded_example %}

<div id="form-group-multiline" class="content">
  <p>
    Add the <code>is-grouped-multiline</code> modifier to allow controls to fill up <strong>multiple lines</strong>. This is ideal for a long list of controls.
  </p>
</div>

{% include elements/snippet.html content=group_multiline_example clipped=true %}

<div class="message is-info">
  <div class="message-header">
    <p>List of buttons</p>
  </div>
  <div class="message-body">
    <div class="content">
      <p>
        If you only need a list of <em>buttons</em>, try out the new <code>buttons</code> class with which you can create a <a href="{{ site.url }}/documentation/elements/button/#list-of-buttons">multiline list of buttons</a>.
      </p>
    </div>
  </div>
</div>

{% include elements/anchor.html name="Horizontal form" %}

<div class="content">
  <p>If you want a <strong>horizontal</strong> form control, use the <code>is-horizontal</code> modifier on the <code>field</code> container, in which you include:</p>
  <ul>
    <li>
      <code>field-label</code> for the side label
    </li>
    <li>
      <code>field-body</code> for the input/select/textarea container
    </li>
  </ul>
  <p>You can use <code>is-grouped</code> or <code>has-addons</code> for the child elements.</p>
</div>

{% include elements/snippet.html content=horizontal_form_example horizontal=true more=true %}

<div class="content">
  <p>
    To preserve the <strong>vertical alignment</strong> of labels with each type and size of control, the <code>field-label</code> comes with <strong>4 size modifiers</strong>:
  </p>
  <ul>
    <li>
      <code>is-small</code>
    </li>
    <li>
      <code>is-normal</code> for any <code>input</code> or <code>button</code>
    </li>
    <li>
      <code>is-medium</code>
    </li>
    <li>
      <code>is-large</code>
    </li>
  </ul>
</div>

{% include elements/snippet.html content=field_label_example horizontal=true more=true %}

{% capture custom_message %}
  Form elements can be <strong>customized</strong> using the following generic variables. Simply set one or multiple of these variables <em>before</em> importing Bulma. <a href="{{ site.url }}/documentation/overview/customize/">Learn how</a>.
{% endcapture %}

{% include elements/anchor.html name="Disabled form" %}

<div class="content">
  <p>
    You can disable part or a whole form by wrapping a set of controls in a <strong>fieldset</strong> with a <code>disabled</code> HTML attribute.
  </p>
</div>

{% include elements/snippet.html content=disabled_fields_example %}

{% include components/variables.html
  anchor_name='Control variables'
  type='element'
  variables_keys=page.variables_control_keys
  custom_message=custom_message
  folder='utilities'
  file='controls'
%}

{% include components/variables.html
  anchor_name='Form variables'
  type='element'
  variables_keys=page.variables_keys
  folder='elements'
  file='form'
  hide_anchor=true
  hide_content=true
%}
