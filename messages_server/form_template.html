<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Form Submission</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #f8f9fa;">
    <div style="max-width: 1000px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">

        <!-- Header -->
        <div style="background: linear-gradient(135deg, #0094ca 0%, #00b8e6 100%); color: #ffffff; padding: 30px 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">
                {if_pricing}💰 New Quote Request{/if_pricing}{if_contact}📧 New Contact Form Submission{/if_contact}
            </h1>
            <div style="margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">
                {if_pricing}A potential customer is interested in your services{/if_pricing}{if_contact}Someone reached out via your website{/if_contact}
            </div>
        </div>

        <!-- Content -->
        <div style="padding: 30px 20px;">

            <!-- Form Type Badge (if pricing) -->
            {if_pricing}<div style="background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%); color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; display: inline-block; margin-bottom: 15px;">PRIORITY</div>{/if_pricing}

            <!-- Contact Information -->
            <div style="background-color: #f8f9fa; border-left: 4px solid #0094ca; padding: 20px; margin: 20px 0; border-radius: 0 6px 6px 0;">
                <h3 style="margin: 0 0 15px 0; color: #0094ca; font-size: 18px; font-weight: 600;">Contact Information</h3>
                <div style="margin: 15px 0;">
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Name:</span>
                        <span style="color: #333333;">{name}</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Email:</span>
                        <span style="color: #333333;">{email}</span>
                    </div>
                    {if_phone}<div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Phone:</span>
                        <span style="color: #333333;">{phone}</span>
                    </div>{/if_phone}
                    {if_company}<div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Company:</span>
                        <span style="color: #333333;">{company}</span>
                    </div>{/if_company}
                    {if_subject}<div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Subject:</span>
                        <span style="color: #333333;">{subject}</span>
                    </div>{/if_subject}
                </div>
            </div>

            <!-- Technical Requirements (pricing form only) -->
            {if_pricing}{if_tech_specs}<div style="background: linear-gradient(135deg, #00efb7 0%, #11ffbd 100%); color: #333333; padding: 20px; margin: 20px 0; border-radius: 6px;">
                <h3 style="margin: 0 0 15px 0; color: #333333; font-size: 18px; font-weight: 600;">📊 Technical Requirements</h3>
                {if_vcpus}<div style="margin-bottom: 8px;"><span style="font-weight: 600; color: #333333; display: inline-block; width: 140px;">vCPUs:</span> {vcpus}</div>{/if_vcpus}
                {if_memory}<div style="margin-bottom: 8px;"><span style="font-weight: 600; color: #333333; display: inline-block; width: 140px;">Memory:</span> {memory} GB</div>{/if_memory}
                {if_blockStorage}<div style="margin-bottom: 8px;"><span style="font-weight: 600; color: #333333; display: inline-block; width: 140px;">Block Storage:</span> {blockStorage} TB</div>{/if_blockStorage}
                {if_objectStorage}<div style="margin-bottom: 8px;"><span style="font-weight: 600; color: #333333; display: inline-block; width: 140px;">Object Storage:</span> {objectStorage} TB</div>{/if_objectStorage}
            </div>{/if_tech_specs}{/if_pricing}

            <!-- Service Details (pricing form only) -->
            {if_pricing}{if_service_details}<div style="background-color: #f8f9fa; border-left: 4px solid #0094ca; padding: 20px; margin: 20px 0; border-radius: 0 6px 6px 0;">
                <h3 style="margin: 0 0 15px 0; color: #0094ca; font-size: 18px; font-weight: 600;">Service Configuration</h3>
                {if_serviceModel}<div style="margin-bottom: 8px;"><span style="font-weight: 600; color: #555555; display: inline-block; width: 140px;">Service Model:</span> {serviceModel}</div>{/if_serviceModel}
                {if_hardwareQuote}<div style="margin-bottom: 8px;"><span style="font-weight: 600; color: #555555; display: inline-block; width: 140px;">Hardware Quote:</span> {hardwareQuote_text}</div>{/if_hardwareQuote}
            </div>{/if_service_details}{/if_pricing}

            <!-- Message -->
            <div style="background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 15px 0; color: #333333; font-size: 18px; font-weight: 600;">Message</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 3px solid #00efb7; font-size: 15px; line-height: 1.6; white-space: pre-wrap;">{message}</div>
            </div>

            <!-- Additional Information (pricing form only) -->
            {if_pricing}{if_additionalInfo}<div style="background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 15px 0; color: #333333; font-size: 18px; font-weight: 600;">Additional Information</h3>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 3px solid #00efb7; font-size: 15px; line-height: 1.6; white-space: pre-wrap;">{additionalInfo}</div>
            </div>{/if_additionalInfo}{/if_pricing}

            <!-- Technical Information -->
            <div style="background-color: #f8f9fa; border-left: 4px solid #0094ca; padding: 20px; margin: 20px 0; border-radius: 0 6px 6px 0;">
                <h3 style="margin: 0 0 15px 0; color: #0094ca; font-size: 18px; font-weight: 600;">Technical Details</h3>
                <div style="margin: 15px 0;">
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">IP Address:</span>
                        <span style="color: #333333;">{ip_address}</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Submitted:</span>
                        <span style="color: #333333;">{timestamp}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #e9ecef;">

            <div style="font-size: 13px; color: #6c757d; margin-top: 10px;">
                Generated on {generated_time}
            </div>
        </div>
    </div>
</body>
</html>
