<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Quote Request</title>
    <style>
        /* Email-safe CSS */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #0094ca 0%, #00b8e6 100%);
            color: #ffffff;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header .subtitle {
            margin: 8px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .info-section {
            background-color: #f8f9fa;
            border-left: 4px solid #0094ca;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .info-section h3 {
            margin: 0 0 15px 0;
            color: #0094ca;
            font-size: 18px;
            font-weight: 600;
        }
        .info-grid {
            display: table;
            width: 100%;
            margin: 15px 0;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            font-weight: 600;
            color: #555555;
            padding: 8px 15px 8px 0;
            vertical-align: top;
            width: 140px;
        }
        .info-value {
            display: table-cell;
            padding: 8px 0;
            vertical-align: top;
            color: #333333;
        }
        .highlight-section {
            background: linear-gradient(135deg, #00efb7 0%, #11ffbd 100%);
            color: #333333;
            padding: 20px;
            margin: 20px 0;
            border-radius: 6px;
        }
        .highlight-section h3 {
            margin: 0 0 15px 0;
            color: #333333;
            font-size: 18px;
            font-weight: 600;
        }
        .specs-grid {
            display: table;
            width: 100%;
        }
        .specs-row {
            display: table-row;
        }
        .specs-label {
            display: table-cell;
            font-weight: 600;
            padding: 8px 15px 8px 0;
            vertical-align: top;
            width: 140px;
        }
        .specs-value {
            display: table-cell;
            padding: 8px 0;
            vertical-align: top;
            font-weight: 500;
        }
        .additional-section {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .additional-section h3 {
            margin: 0 0 15px 0;
            color: #333333;
            font-size: 18px;
            font-weight: 600;
        }
        .additional-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #00efb7;
            font-size: 15px;
            line-height: 1.6;
            white-space: pre-wrap;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 0;
            font-size: 14px;
            color: #6c757d;
        }
        .brand {
            color: #0094ca;
            font-weight: 600;
        }
        .timestamp {
            font-size: 13px;
            color: #6c757d;
            margin-top: 10px;
        }
        .priority-badge {
            background-color: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            .header, .content, .footer {
                padding: 20px 15px;
            }
            .info-grid, .specs-grid {
                display: block;
            }
            .info-row, .specs-row {
                display: block;
                margin-bottom: 10px;
            }
            .info-label, .info-value, .specs-label, .specs-value {
                display: block;
                width: 100%;
                padding: 4px 0;
            }
            .info-label, .specs-label {
                font-weight: 600;
                color: #0094ca;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>💰 New Quote Request</h1>
            <div class="subtitle">A potential customer is interested in your services</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Contact Information -->
            <div class="info-section">
                <h3>Contact Information</h3>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">Name:</div>
                        <div class="info-value">{name}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Email:</div>
                        <div class="info-value">{email}</div>
                    </div>
                    {phone_row}
                    {company_row}
                </div>
            </div>
            
            <!-- Technical Requirements -->
            <div class="highlight-section">
                <h3>📊 Technical Requirements</h3>
                <div class="specs-grid">
                    {technical_specs}
                </div>
            </div>
            
            <!-- Service Details -->
            <div class="info-section">
                <h3>Service Configuration</h3>
                <div class="info-grid">
                    {service_details}
                </div>
            </div>
            
            <!-- Additional Information -->
            {additional_info_section}
            
            <!-- Technical Information -->
            <div class="info-section">
                <h3>Technical Details</h3>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-label">IP Address:</div>
                        <div class="info-value">{ip_address}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Submitted:</div>
                        <div class="info-value">{timestamp}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>This quote request was submitted via the pricing page on <span class="brand">whitesky.cloud</span></p>
            <div class="timestamp">Generated on {generated_time}</div>
        </div>
    </div>
</body>
</html>
