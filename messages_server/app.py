#!/usr/bin/env python3
import json
import logging
import os
import smtplib
import sys
import time
from collections import defaultdict
from datetime import datetime
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from threading import Lock
import atexit

from dotenv import load_dotenv
from flask import Flask, jsonify, request
from flask_cors import CORS
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure logging
handler = logging.StreamHandler(sys.stdout)
handler.setLevel(logging.INFO)
formatter = logging.Formatter(
    '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
)
handler.setFormatter(formatter)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(handler)

# Rate limiting configuration
class TokenBucket:
    def __init__(self, max_tokens=5, refill_rate=1/60):  # 1 token per minute
        self.max_tokens = max_tokens
        self.tokens = max_tokens
        self.refill_rate = refill_rate
        self.last_refill = time.time()
        self.lock = Lock()

    def consume(self):
        with self.lock:
            now = time.time()
            # Add tokens based on time passed
            tokens_to_add = (now - self.last_refill) * self.refill_rate
            self.tokens = min(self.max_tokens, self.tokens + tokens_to_add)
            self.last_refill = now

            if self.tokens >= 1:
                self.tokens -= 1
                return True
            return False

# Store rate limiting buckets per IP
rate_limit_buckets = defaultdict(lambda: TokenBucket())

# Form submission storage for daily reporting
class FormSubmissionStorage:
    def __init__(self):
        self.submissions = []
        self.lock = Lock()

    def add_submission(self, form_type, name, email, subject, message, ip_address, timestamp):
        """Add a form submission to daily storage"""
        with self.lock:
            submission = {
                'form_type': form_type,
                'name': name,
                'email': email,
                'subject': subject,
                'message': message,
                'ip_address': ip_address,
                'timestamp': timestamp,
                'datetime_obj': datetime.now()
            }
            self.submissions.append(submission)
            logger.info(f"Form submission logged: {form_type} form from {name} ({email})")

    def get_submissions(self):
        """Get all submissions without clearing"""
        with self.lock:
            return self.submissions.copy()

    def clear_submissions(self):
        """Clear all submissions"""
        with self.lock:
            self.submissions.clear()

    def get_submission_count(self):
        """Get current number of stored submissions"""
        with self.lock:
            return len(self.submissions)

# Global form submission storage
form_storage = FormSubmissionStorage()

# Email configuration
SMTP_SERVER = os.getenv('SMTP_SERVER')
SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
SMTP_USERNAME = os.getenv('SMTP_USERNAME')
SMTP_PASSWORD = os.getenv('SMTP_PASSWORD')
FROM_EMAIL = os.getenv('FROM_EMAIL', SMTP_USERNAME)
TO_EMAIL = os.getenv('TO_EMAIL')

def send_email(subject, body, is_html=False):
    """Send email using SMTP configuration"""
    try:
        # Create message
        msg = MIMEMultipart()
        msg["From"] = FROM_EMAIL
        msg["To"] = TO_EMAIL
        msg["Subject"] = subject
        msg['Reply-To'] = FROM_EMAIL

        # Add body (HTML or plain text)
        if is_html:
            msg.attach(MIMEText(body, 'html'))
        else:
            msg.attach(MIMEText(body, 'plain'))

        # Send email
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.ehlo()
            server.starttls()
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.sendmail(FROM_EMAIL, TO_EMAIL, msg.as_string())

        logger.info(f"Email sent successfully to {TO_EMAIL}")
        return True

    except Exception as e:
        logger.info(f"Failed to send email: {str(e)}")
        return False

def generate_daily_summary_html(submissions):
    """Generate HTML email template for daily form submission summary"""
    try:
        # Read template file
        template_path = os.path.join(os.path.dirname(__file__), 'daily_summary_template.html')
        with open(template_path, 'r', encoding='utf-8') as f:
            template = f.read()

        if not submissions:
            return template.format(
                total_submissions=0,
                date=datetime.now().strftime('%Y-%m-%d'),
                form_type_summary="<li>No submissions received today</li>",
                form_sections="",
                generated_time=datetime.now().strftime('%Y-%m-%d at %H:%M:%S')
            )

        # Group submissions by form type
        grouped_submissions = defaultdict(list)
        for submission in submissions:
            grouped_submissions[submission['form_type']].append(submission)

        # Generate form type summary
        form_type_summary = ""
        for form_type, form_submissions in grouped_submissions.items():
            form_type_summary += f"<li><strong>{form_type.title()} Forms:</strong> {len(form_submissions)} submissions</li>"

        # Generate form sections
        form_sections = ""
        for form_type, form_submissions in grouped_submissions.items():
            form_sections += f'<div class="form-section"><h2 class="form-title">{form_type.title()} Form Submissions ({len(form_submissions)})</h2>'

            for i, submission in enumerate(form_submissions, 1):
                form_sections += f'''
                <div class="submission">
                    <div class="submission-header">Submission #{i} - {submission['timestamp']}</div>
                    <div class="field"><span class="field-label">Name:</span> {submission['name']}</div>
                    <div class="field"><span class="field-label">Email:</span> {submission['email']}</div>
                    <div class="field"><span class="field-label">Subject:</span> {submission['subject'] or 'No subject'}</div>
                    <div class="field"><span class="field-label">IP Address:</span> {submission['ip_address']}</div>
                    <div class="field">
                        <span class="field-label">Message:</span>
                        <div class="message-content">{submission['message'].replace(chr(10), '<br>')}</div>
                    </div>
                </div>
                '''

            form_sections += "</div>"

        return template.format(
            total_submissions=len(submissions),
            date=datetime.now().strftime('%Y-%m-%d'),
            form_type_summary=form_type_summary,
            form_sections=form_sections,
            generated_time=datetime.now().strftime('%Y-%m-%d at %H:%M:%S')
        )

    except Exception as e:
        logger.info(f"Error generating HTML template: {str(e)}")
        return "Failed to generating daily summary email."

def send_daily_summary():
    """Send daily summary email with all form submissions"""
    try:
        submissions = form_storage.get_submissions()

        if submissions:
            logger.info(f"Sending daily summary with {len(submissions)} form submissions")
        else:
            logger.info("Sending daily summary with no submissions")

        # Generate HTML email content
        html_content = generate_daily_summary_html(submissions)

        # Send email
        subject = f"Daily Form Submissions Summary - {datetime.now().strftime('%Y-%m-%d')} ({len(submissions)} submissions)"

        if send_email(subject, html_content, is_html=True):
            logger.info(f"Daily summary email sent successfully with {len(submissions)} submissions")
            # Only clear submissions on successful email send
            form_storage.clear_submissions()
        else:
            logger.info(f"Failed to send daily summary email")

    except Exception as e:
        logger.info(f"Error sending daily summary: {str(e)}")

# Initialize scheduler for daily email reports
scheduler = BackgroundScheduler()
scheduler.add_job(
    func=send_daily_summary,
    trigger=CronTrigger(hour=20, minute=20, timezone='Europe/Berlin'),  # 8:20 PM CEST
    id='daily_summary',
    name='Send daily form submission summary',
    replace_existing=True
)

def start_scheduler():
    """Start the background scheduler"""
    try:
        scheduler.start()
        logger.info("Daily email scheduler started - will send summary at 8:20 PM CEST daily")
    except Exception as e:
        logger.info(f"Failed to start scheduler: {str(e)}")

def shutdown_scheduler():
    """Shutdown the scheduler gracefully"""
    try:
        scheduler.shutdown()
        logger.info("Scheduler shutdown successfully")
    except Exception as e:
        logger.info(f"Error shutting down scheduler: {str(e)}")

# Register shutdown handler
atexit.register(shutdown_scheduler)

def get_client_ip():
    """Get client IP address, considering proxies"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def validate_bot_detection_data(bot_data_str):
    """
    Validate bot detection data from client-side
    Returns (is_bot, reason) tuple
    """
    try:
        if not bot_data_str:
            logger.info("Missing bot detection data")
            return True, "missing_data"

        bot_data = json.loads(bot_data_str)

        # Check honeypot field
        if bot_data.get('honeypotFilled', False):
            logger.info("Bot detected: honeypot filled")
            return True, "honeypot_filled"

        # Check submission timing (too fast)
        submission_time = bot_data.get('submissionTime', 0)
        if submission_time < 2000:  # Less than 2 seconds
            logger.info(f"Bot detected: too fast submission ({submission_time}ms)")
            return True, "too_fast"

        # Check for human interaction indicators
        has_mouse_movement = bot_data.get('hasMouseMovement', False)
        has_field_focus = bot_data.get('hasFieldFocus', False)
        mouse_move_count = bot_data.get('mouseMoveCount', 0)
        keyboard_events = bot_data.get('keyboardEvents', 0)
        field_focus_events = bot_data.get('fieldFocusEvents', 0)

        # Bot if no interaction at all
        if not has_mouse_movement and not has_field_focus and keyboard_events == 0:
            logger.info("Bot detected: no human interaction")
            return True, "no_interaction"

        # Bot if very minimal interaction (likely automated)
        if mouse_move_count < 3 and keyboard_events < 5 and field_focus_events < 2:
            logger.info("Bot detected: minimal interaction")
            return True, "minimal_interaction"

        # Check user agent for obvious bots
        user_agent = bot_data.get('userAgent', '').lower()
        bot_indicators = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget']
        if any(indicator in user_agent for indicator in bot_indicators):
            logger.info(f"Bot detected: suspicious user agent - {user_agent}")
            return True, "suspicious_user_agent"

        # Passed all checks - likely human
        logger.info("Bot validation passed - appears to be human")
        return False, "human"

    except json.JSONDecodeError:
        logger.info("Bot detected: invalid JSON in bot detection data")
        return True, "invalid_json"
    except Exception as e:
        logger.info(f"Error validating bot detection data: {str(e)}")
        return True, "validation_error"

@app.route('/submit-form', methods=['POST'])
def submit_form():
    """Handle contact form submissions"""
    try:
        # Get client IP for rate limiting
        client_ip = get_client_ip()

        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        subject = request.form.get('subject', '').strip()
        message = request.form.get('message', '').strip()
        bot_detection_data = request.form.get('_bot_detection_data', '')

        # Determine form type based on message content and referrer
        form_type = "contact"  # default
        if message and message.startswith("QUOTE REQUEST DETAILS:"):
            form_type = "pricing"

        # Add ALL submissions to storage regardless of validation outcome
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        form_storage.add_submission(
            form_type=form_type,
            name=name or 'Unknown',
            email=email or 'Unknown',
            subject=subject,
            message=message or 'No message',
            ip_address=client_ip,
            timestamp=timestamp
        )

        # Check rate limit
        bucket = rate_limit_buckets[client_ip]
        if not bucket.consume():
            logger.info(f"Rate limit exceeded for IP: {client_ip}")
            # Return success but don't send email
            return jsonify({
                'success': True,
                'message': 'Thank you! Your message has been received.'
            }), 200

        # Validate bot detection data
        is_bot, bot_reason = validate_bot_detection_data(bot_detection_data)

        if is_bot:
            logger.info(f"Bot submission blocked from IP {client_ip}, reason: {bot_reason}")
            # Return success to maintain user experience but don't send email
            return jsonify({
                'success': True,
                'message': 'Thank you! Your message has been received.'
            }), 200

        # Validate required fields
        if not name or not email or not message:
            return jsonify({
                'success': False,
                'message': 'Please fill in all required fields.'
            }), 400

        # Basic email validation
        if '@' not in email or '.' not in email.split('@')[1]:
            return jsonify({
                'success': False,
                'message': 'Please enter a valid email address.'
            }), 400

        # Prepare email content
        if not subject:
            subject = "Contact Form Submission"

        # Check if this is a pricing form (has pre-built message)
        if form_type == "pricing":
            # This is a pricing form with detailed quote information
            email_body = f"""
New quote request from {name}

Contact Information:
- Name: {name}
- Email: {email}
- IP Address: {client_ip}
- Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{message}
"""
        else:
            # This is a regular contact form or section5 form
            email_body = f"""
New contact form submission from {name}

Contact Information:
- Name: {name}
- Email: {email}
- IP Address: {client_ip}
- Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Message:
{message}
"""

        # Send email
        if send_email(subject, email_body, name, email):
            logger.info(f"Form submission successful: {form_type} form from {name} ({email}) - Subject: {subject}")
            return jsonify({
                'success': True,
                'message': 'Thank you! Your message has been sent successfully.'
            }), 200
        else:
            logger.info(f"Failed to send email for submission from {name} ({email})")
            return jsonify({
                'success': False,
                'message': 'There was a problem sending your message. Please try again.'
            }), 500

    except Exception as e:
        logger.info(f"Error processing form submission: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An unexpected error occurred. Please try again.'
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'pending_submissions': form_storage.get_submission_count()
    }), 200

@app.route('/test-daily-summary', methods=['POST'])
def test_daily_summary():
    """Test endpoint to manually trigger daily summary email"""
    try:
        send_daily_summary()
        return jsonify({
            'success': True,
            'message': 'Daily summary email sent successfully'
        }), 200
    except Exception as e:
        logger.info(f"Error sending test daily summary: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Failed to send daily summary: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': 'Endpoint not found'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'message': 'Method not allowed'
    }), 405

if __name__ == '__main__':
    # Validate required environment variables
    if not SMTP_USERNAME or not SMTP_PASSWORD or not TO_EMAIL:
        logger.info("Missing required environment variables: SMTP_USERNAME, SMTP_PASSWORD, TO_EMAIL")
        exit(1)

    # Start the daily email scheduler
    start_scheduler()

    # Run the application
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'

    logger.info(f"Starting contact form server on port {port}")
    logger.info(f"Daily email summary will be sent at 8:20 PM CEST")
    app.run(host='0.0.0.0', port=port, debug=debug)
