#!/usr/bin/env python3
"""
Test script for HTML email templates
"""

import os
import sys
from datetime import datetime

# Add the current directory to the path so we can import from app.py
sys.path.insert(0, os.path.dirname(__file__))

from app import (
    generate_contact_form_html,
    generate_section5_form_html,
    generate_pricing_form_html
)

def test_contact_form_template():
    """Test the contact form HTML template"""
    print("Testing Contact Form Template...")
    
    html = generate_contact_form_html(
        name="<PERSON>",
        email="<EMAIL>",
        subject="General Inquiry",
        message="Hello, I'm interested in your cloud services. Can you provide more information about your pricing and features?",
        phone="+****************",
        ip_address="*************",
        timestamp="2024-01-15 14:30:25"
    )
    
    if html:
        with open('test_contact_form.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Contact form template generated successfully -> test_contact_form.html")
    else:
        print("❌ Failed to generate contact form template")

def test_section5_form_template():
    """Test the section5 form HTML template"""
    print("Testing Section5 Form Template...")
    
    html = generate_section5_form_html(
        name="<PERSON> <PERSON>",
        email="<EMAIL>",
        subject="Partnership Opportunity",
        message="Hi there! We're looking for a reliable cloud provider for our startup. Would love to discuss potential partnership opportunities.",
        ip_address="*********",
        timestamp="2024-01-15 16:45:12"
    )
    
    if html:
        with open('test_section5_form.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Section5 form template generated successfully -> test_section5_form.html")
    else:
        print("❌ Failed to generate section5 form template")

def test_pricing_form_template():
    """Test the pricing form HTML template"""
    print("Testing Pricing Form Template...")
    
    # Sample pricing form message (as generated by the frontend)
    pricing_message = """QUOTE REQUEST DETAILS:

Contact Information:
- Phone: +32 9 123 4567
- Company: TechCorp Solutions

Technical Requirements:
- vCPUs: 32
- Memory: 128 GB
- Block Storage: 5 TB
- Object Storage: 20 TB
- Service Model: caas
- Hardware Quote Requested: Yes

Additional Information:
We need GPU support for machine learning workloads (NVIDIA A100 preferred). Also interested in managed Kubernetes service and database as a service options. Timeline is flexible but would like to start within 2-3 months."""
    
    html = generate_pricing_form_html(
        name="Alex Johnson",
        email="<EMAIL>",
        message=pricing_message,
        ip_address="************",
        timestamp="2024-01-15 11:20:33"
    )
    
    if html:
        with open('test_pricing_form.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("✅ Pricing form template generated successfully -> test_pricing_form.html")
    else:
        print("❌ Failed to generate pricing form template")

def main():
    """Run all template tests"""
    print("🧪 Testing HTML Email Templates\n")
    
    test_contact_form_template()
    print()
    
    test_section5_form_template()
    print()
    
    test_pricing_form_template()
    print()
    
    print("📧 Test complete! Check the generated HTML files to preview the email templates.")
    print("💡 You can open these files in a web browser to see how they look.")

if __name__ == "__main__":
    main()
